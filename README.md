# 🧠 MRI Tumor Detection System

A deep learning-powered web application for detecting brain tumors in MRI scans using Convolutional Neural Networks (CNN). This system can classify MRI images into four categories: Glioma, Meningioma, Pituitary tumor, or No tumor.

## ⚠️ **IMPORTANT MEDICAL DISCLAIMER**

**THIS SYSTEM IS FOR E<PERSON>UCATIONAL AND RESEARCH PURPOSES ONLY. IT IS NOT INTENDED FOR CLINICAL USE OR MEDICAL DIAGNOSIS.**

- **NOT A SUBSTITUTE FOR PROFESSIONAL MEDICAL ADVICE**: This AI system should never replace consultation with qualified medical professionals
- **NOT CLINICALLY VALIDATED**: This model has not undergone clinical trials or regulatory approval
- **POTENTIAL FOR ERRORS**: AI systems can produce false positives and false negatives
- **SEEK PROFESSIONAL HELP**: Always consult with radiologists and medical doctors for actual medical diagnosis
- **EMERGENCY SITUATIONS**: Never rely on this system for urgent medical decisions

## 🎯 What This Project Does

This web application provides:

- **Automated MRI Analysis**: Upload MRI brain scans for automated tumor detection
- **Multi-class Classification**: Identifies four types of conditions:
  - **Glioma**: A type of brain tumor that begins in glial cells
  - **Meningioma**: A tumor that arises from the meninges (membranes surrounding the brain)
  - **Pituitary**: A tumor in the pituitary gland
  - **No Tumor**: Healthy brain tissue with no detectable tumor
- **Confidence Scoring**: Provides confidence percentages for predictions
- **User-friendly Interface**: Simple web interface for easy image upload and result visualization

## 🤖 Model Architecture

- **Model Type**: Convolutional Neural Network (CNN)
- **Framework**: TensorFlow/Keras
- **Input Size**: 128x128 pixels
- **Architecture**: Deep CNN with multiple convolutional and pooling layers
- **Output**: 4-class classification with softmax activation
- **Model File**: `models/model.h5`

## 📊 Model Performance

**Note**: The exact accuracy metrics should be validated through proper testing procedures. Typical performance for similar CNN models on brain tumor classification tasks ranges from 85-95% accuracy on test datasets.

**Important Considerations**:
- Performance may vary significantly on real-world data
- Model was trained on specific dataset conditions
- Results may not generalize to all MRI scanner types or imaging protocols
- Always validate results with medical professionals

## 🚀 Getting Started

### Prerequisites

- Python 3.10
- Conda (recommended) or Python virtual environment
- At least 4GB RAM (for TensorFlow operations)
- Modern web browser

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/deathvadeR-afk/MRI_SCAN_detection.git
   cd MRI_Detection
   ```

2. **Create and activate virtual environment**:
   ```bash
   # Using Conda (recommended)
   conda create -p ./venv python=3.10 -y
   conda activate ./venv
   
   # Or using Python venv
   python -m venv venv
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python main.py
   ```

5. **Access the web interface**:
   - Open your browser and navigate to `http://localhost:5000`
   - Upload an MRI image using the web interface
   - View the prediction results with confidence scores

### Usage

1. **Prepare MRI Images**: Ensure your MRI images are in common formats (JPEG, PNG, etc.)
2. **Upload Image**: Use the web interface to upload your MRI scan
3. **View Results**: The system will display:
   - Predicted tumor type or "No Tumor"
   - Confidence percentage
   - The uploaded image for reference

## 📁 Project Structure

```
MRI_Detection/
├── main.py                 # Flask web application
├── requirements.txt        # Python dependencies
├── models/
│   └── model.h5           # Trained CNN model
├── templates/
│   └── index.html         # Web interface template
├── uploads/               # Temporary storage for uploaded images
```

## 🛠️ Technical Details

### Dependencies

- **TensorFlow 2.19.0**: Deep learning framework
- **Keras 3.10.0**: High-level neural networks API
- **Flask 3.1.1**: Web framework
- **NumPy**: Numerical computing
- **Pillow**: Image processing
- **H5py**: HDF5 file format support

### Image Processing Pipeline

1. **Image Loading**: Load uploaded image using Keras preprocessing
2. **Resizing**: Resize to 128x128 pixels
3. **Normalization**: Pixel values normalized to [0,1] range
4. **Batch Preparation**: Add batch dimension for model input
5. **Prediction**: Forward pass through CNN model
6. **Post-processing**: Extract class prediction and confidence score

## ⚠️ Limitations and Considerations

### Technical Limitations
- **Image Quality**: Performance depends on MRI image quality and resolution
- **Scanner Variability**: May not work optimally with all MRI scanner types
- **Dataset Bias**: Model performance limited by training data diversity
- **Preprocessing**: Requires specific image preprocessing steps

### Medical Limitations
- **Not Diagnostic**: Cannot replace professional radiological interpretation
- **False Results**: May produce incorrect classifications
- **Limited Scope**: Only trained on specific tumor types
- **No Treatment Guidance**: Does not provide treatment recommendations

## 🔬 For Researchers and Developers

### Model Training
- The model was trained on a dataset of brain MRI images
- Uses data augmentation techniques to improve generalization
- Implements dropout and regularization to prevent overfitting

### Extending the Project
- **Add More Classes**: Extend to detect additional tumor types
- **Improve Accuracy**: Implement more sophisticated architectures (ResNet, EfficientNet)
- **Data Augmentation**: Add more robust preprocessing pipelines
- **Ensemble Methods**: Combine multiple models for better performance

## 📝 License

This project is for educational purposes. Please ensure compliance with medical data regulations and ethical guidelines when using or modifying this code.

## 🤝 Contributing

Contributions are welcome! Please ensure any medical-related contributions are reviewed by qualified medical professionals.

## 📞 Support

For technical issues, please create an issue in the repository. For medical questions, consult with healthcare professionals.

---

**Remember: This is an educational tool. Always consult medical professionals for health-related decisions.**
