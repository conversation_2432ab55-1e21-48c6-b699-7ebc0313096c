<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tumor Detection System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
        }

        .card {
            border-radius: 15px;
        }

        #results img {
            max-height: 400px;
            margin-top: 15px;
        }

        .text-center {
            text-align: center
        }

        .btn {
            width: 100%
        }

        .lead {
            margin-bottom: 30px;
        }
    </style>
</head>

<body>
    <div class="container mt-5">
        <div class="text-center">
            <h1 class="display-4 text-primary">MRI Tumor Detection System</h1>
            <p class="lead text-secondary">Upload an MRI image to detect if there is a tumor and its type.</p>
        </div>

        <!-- File Upload Form -->
        <div class="card shadow p-4 mt-4">
            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="file" class="form-label">Select MRI Image:</label>
                    <input type="file" class="form-control" id="file" name="file" accept="image/*" required>
                </div>
                <button type="submit" class="btn btn-primary">Upload and Detect</button>
            </form>
        </div>

        {% if result %}
        <!-- Display Results -->
        <div id="results" class="mt-4">
            <div class="card shadow">
                <div class="card-body">
                    <h4 class="card-title text-success">{{ result }}</h4>
                    <p class="card-text text-muted">Confidence: {{ confidence }}%</p>
                    <img src="{{ file_path }}" class="img-fluid rounded" alt="Uploaded Image">
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</body>
</html>